<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res"><file name="fade_in_text" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\anim\fade_in_text.xml" qualifiers="" type="anim"/><file name="pulse_animation" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\anim\pulse_animation.xml" qualifiers="" type="anim"/><file name="slide_in" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\anim\slide_in.xml" qualifiers="" type="anim"/><file name="slide_out" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\anim\slide_out.xml" qualifiers="" type="anim"/><file name="angina_logo" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\angina_logo.png" qualifiers="" type="drawable"/><file name="baseline_arrow_back_ios_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_arrow_back_ios_24.xml" qualifiers="" type="drawable"/><file name="baseline_arrow_forward_ios_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_arrow_forward_ios_24.xml" qualifiers="" type="drawable"/><file name="baseline_content_copy_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_content_copy_24.xml" qualifiers="" type="drawable"/><file name="baseline_keyboard_arrow_right_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_keyboard_arrow_right_24.xml" qualifiers="" type="drawable"/><file name="baseline_refresh_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_refresh_24.xml" qualifiers="" type="drawable"/><file name="baseline_repeat_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_repeat_24.xml" qualifiers="" type="drawable"/><file name="baseline_settings_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_settings_24.xml" qualifiers="" type="drawable"/><file name="baseline_share_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_share_24.xml" qualifiers="" type="drawable"/><file name="baseline_text_fields_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_text_fields_24.xml" qualifiers="" type="drawable"/><file name="baseline_wrap_text_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\baseline_wrap_text_24.xml" qualifiers="" type="drawable"/><file name="blank_image" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\blank_image.png" qualifiers="" type="drawable"/><file name="button_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\button_back.xml" qualifiers="" type="drawable"/><file name="button_backround" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\button_backround.xml" qualifiers="" type="drawable"/><file name="constant_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\constant_back.xml" qualifiers="" type="drawable"/><file name="constant_second_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\constant_second_back.xml" qualifiers="" type="drawable"/><file name="copy_share_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\copy_share_back.xml" qualifiers="" type="drawable"/><file name="decoration_image" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\decoration_image.png" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dot_active" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\dot_active.xml" qualifiers="" type="drawable"/><file name="dot_inactive" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\dot_inactive.xml" qualifiers="" type="drawable"/><file name="drawer_color_item" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\drawer_color_item.xml" qualifiers="" type="drawable"/><file name="edittext_backround" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\edittext_backround.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="image_art" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\image_art.png" qualifiers="" type="drawable"/><file name="image_stylish" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\image_stylish.png" qualifiers="" type="drawable"/><file name="info" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\info.xml" qualifiers="" type="drawable"/><file name="loading_background" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\loading_background.xml" qualifiers="" type="drawable"/><file name="logo" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\logo.xml" qualifiers="" type="drawable"/><file name="message_image" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\message_image.png" qualifiers="" type="drawable"/><file name="night_mode" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\night_mode.xml" qualifiers="" type="drawable"/><file name="privacy" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\privacy.xml" qualifiers="" type="drawable"/><file name="pulsing_dot" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\pulsing_dot.xml" qualifiers="" type="drawable"/><file name="question_mark" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\question_mark.xml" qualifiers="" type="drawable"/><file name="random_image" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\random_image.png" qualifiers="" type="drawable"/><file name="repeater_image" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\repeater_image.png" qualifiers="" type="drawable"/><file name="settings_layout_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\settings_layout_back.xml" qualifiers="" type="drawable"/><file name="share" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\share.xml" qualifiers="" type="drawable"/><file name="sms_item_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\sms_item_back.xml" qualifiers="" type="drawable"/><file name="star" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\star.xml" qualifiers="" type="drawable"/><file name="text_to_emoji" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\text_to_emoji.png" qualifiers="" type="drawable"/><file name="view_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable\view_back.xml" qualifiers="" type="drawable"/><file name="baseline_arrow_forward_ios_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable-night\baseline_arrow_forward_ios_24.xml" qualifiers="night-v8" type="drawable"/><file name="baseline_refresh_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable-night\baseline_refresh_24.xml" qualifiers="night-v8" type="drawable"/><file name="baseline_settings_24" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable-night\baseline_settings_24.xml" qualifiers="night-v8" type="drawable"/><file name="copy_share_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable-night\copy_share_back.xml" qualifiers="night-v8" type="drawable"/><file name="view_back" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\drawable-night\view_back.xml" qualifiers="night-v8" type="drawable"/><file name="abhaya_libre" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\abhaya_libre.xml" qualifiers="" type="font"/><file name="abril_fatface" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\abril_fatface.xml" qualifiers="" type="font"/><file name="acme" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\acme.xml" qualifiers="" type="font"/><file name="adamina" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\adamina.xml" qualifiers="" type="font"/><file name="advent_pro_medium" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\advent_pro_medium.xml" qualifiers="" type="font"/><file name="advent_pro_semibold" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\advent_pro_semibold.xml" qualifiers="" type="font"/><file name="afacad_medium" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\afacad_medium.xml" qualifiers="" type="font"/><file name="agbalumo" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\agbalumo.xml" qualifiers="" type="font"/><file name="aladin" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\aladin.xml" qualifiers="" type="font"/><file name="alata" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\alata.xml" qualifiers="" type="font"/><file name="alatsi" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\alatsi.xml" qualifiers="" type="font"/><file name="aleo_medium_italic" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\aleo_medium_italic.xml" qualifiers="" type="font"/><file name="alexandria" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\alexandria.xml" qualifiers="" type="font"/><file name="alice" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\alice.xml" qualifiers="" type="font"/><file name="alkatra" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\alkatra.xml" qualifiers="" type="font"/><file name="alkatra_medium" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\alkatra_medium.xml" qualifiers="" type="font"/><file name="allerta" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\allerta.xml" qualifiers="" type="font"/><file name="almendra" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\almendra.xml" qualifiers="" type="font"/><file name="amaranth" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\amaranth.xml" qualifiers="" type="font"/><file name="amaranth_bold" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\amaranth_bold.xml" qualifiers="" type="font"/><file name="andika_italic" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\andika_italic.xml" qualifiers="" type="font"/><file name="anek_bangla_medium" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\anek_bangla_medium.xml" qualifiers="" type="font"/><file name="archivo_black" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\archivo_black.xml" qualifiers="" type="font"/><file name="arima_madurai_bold" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\arima_madurai_bold.xml" qualifiers="" type="font"/><file name="arima_madurai_medium" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\arima_madurai_medium.xml" qualifiers="" type="font"/><file name="artifika" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\font\artifika.xml" qualifiers="" type="font"/><file name="activity_blank_text" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_blank_text.xml" qualifiers="" type="layout"/><file name="activity_decoration_text" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_decoration_text.xml" qualifiers="" type="layout"/><file name="activity_emoji_art" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_emoji_art.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_message" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_message.xml" qualifiers="" type="layout"/><file name="activity_navigation" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_navigation.xml" qualifiers="" type="layout"/><file name="activity_notification_permission" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_notification_permission.xml" qualifiers="" type="layout"/><file name="activity_random_text" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_random_text.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_splash_screen" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_splash_screen.xml" qualifiers="" type="layout"/><file name="activity_stylish_font" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_stylish_font.xml" qualifiers="" type="layout"/><file name="activity_text_repeat" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_text_repeat.xml" qualifiers="" type="layout"/><file name="activity_text_to_imoji" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\activity_text_to_imoji.xml" qualifiers="" type="layout"/><file name="dialog_about_us" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\dialog_about_us.xml" qualifiers="" type="layout"/><file name="dialog_sms_bottom_sheet" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\dialog_sms_bottom_sheet.xml" qualifiers="" type="layout"/><file name="emoji_art_layout" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\emoji_art_layout.xml" qualifiers="" type="layout"/><file name="emoji_dailog" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\emoji_dailog.xml" qualifiers="" type="layout"/><file name="enoji_item_layout" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\enoji_item_layout.xml" qualifiers="" type="layout"/><file name="fragment_dynamic_category" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\fragment_dynamic_category.xml" qualifiers="" type="layout"/><file name="fragment_funny_" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\fragment_funny_.xml" qualifiers="" type="layout"/><file name="fragment_romantic_s_" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\fragment_romantic_s_.xml" qualifiers="" type="layout"/><file name="fragment_sad_" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\fragment_sad_.xml" qualifiers="" type="layout"/><file name="fragment_sms" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\fragment_sms.xml" qualifiers="" type="layout"/><file name="item_repeat" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\item_repeat.xml" qualifiers="" type="layout"/><file name="item_sms" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\item_sms.xml" qualifiers="" type="layout"/><file name="item_stylish_text" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\item_stylish_text.xml" qualifiers="" type="layout"/><file name="message_item" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\message_item.xml" qualifiers="" type="layout"/><file name="slider_screen" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\slider_screen.xml" qualifiers="" type="layout"/><file name="sms_item_dailog" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\layout\sms_item_dailog.xml" qualifiers="" type="layout"/><file name="settings_item" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\menu\settings_item.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="sub_layout_color">#EEEEEE</color><color name="mother_layout_color">#FFFFFFFF</color><color name="primary_text">#E6000000</color><color name="love">#ff819f</color><color name="navigation_color">#EEEEEE</color><color name="status_bar_light">#FFFFFFFF</color><color name="secondary_text">#8F000000</color><color name="toolbar_text_color">#B8000000</color><color name="copy_color">#0FE91E63</color><color name="view_color">#31000000</color><color name="emoji_view_color">#14000000</color></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values\font_certs.xml" qualifiers=""><array name="com_google_android_gms_fonts_certs">
        <item>@array/com_google_android_gms_fonts_certs_dev</item>
        <item>@array/com_google_android_gms_fonts_certs_prod</item>
    </array><string-array name="com_google_android_gms_fonts_certs_dev">
        <item>
            MIIEqDCCA5CgAwIBAgIJANWFuGx90071MA0GCSqGSIb3DQEBBAUAMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTAeFw0wODA0MTUyMzM2NTZaFw0zNTA5MDEyMzM2NTZaMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBANbOLggKv+IxTdGNs8/TGFy0PTP6DHThvbbR24kT9ixcOd9W+EaBPWW+wPPKQmsHxajtWjmQwWfna8mZuSeJS48LIgAZlKkpFeVyxW0qMBujb8X8ETrWy550NaFtI6t9+u7hZeTfHwqNvacKhp1RbE6dBRGWynwMVX8XW8N1+UjFaq6GCJukT4qmpN2afb8sCjUigq0GuMwYXrFVee74bQgLHWGJwPmvmLHC69EH6kWr22ijx4OKXlSIx2xT1AsSHee70w5iDBiK4aph27yH3TxkXy9V89TDdexAcKk/cVHYNnDBapcavl7y0RiQ4biu8ymM8Ga/nmzhRKya6G0cGw8CAQOjgfwwgfkwHQYDVR0OBBYEFI0cxb6VTEM8YYY6FbBMvAPyT+CyMIHJBgNVHSMEgcEwgb6AFI0cxb6VTEM8YYY6FbBMvAPyT+CyoYGapIGXMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbYIJANWFuGx90071MAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQEEBQADggEBABnTDPEF+3iSP0wNfdIjIz1AlnrPzgAIHVvXxunW7SBrDhEglQZBbKJEk5kT0mtKoOD1JMrSu1xuTKEBahWRbqHsXclaXjoBADb0kkjVEJu/Lh5hgYZnOjvlba8Ld7HCKePCVePoTJBdI4fvugnL8TsgK05aIskyY0hKI9L8KfqfGTl1lzOv2KoWD0KWwtAWPoGChZxmQ+nBli+gwYMzM1vAkP+aayLe0a1EQimlOalO762r0GXO0ks+UeXde2Z4e+8S/pf7pITEI/tP+MxJTALw9QUWEv9lKTk+jkbqxbsh8nfBUapfKqYn0eidpwq2AzVp3juYl7//fKnaPhJD9gs=
        </item>
    </string-array><string-array name="com_google_android_gms_fonts_certs_prod">
        <item>
            MIIEQzCCAyugAwIBAgIJAMLgh0ZkSjCNMA0GCSqGSIb3DQEBBAUAMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDAeFw0wODA4MjEyMzEzMzRaFw0zNjAxMDcyMzEzMzRaMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBAKtWLgDYO6IIrgqWbxJOKdoR8qtW0I9Y4sypEwPpt1TTcvZApxsdyxMJZ2JORland2qSGT2y5b+3JKkedxiLDmpHpDsz2WCbdxgxRczfey5YZnTJ4VZbH0xqWVW/8lGmPav5xVwnIiJS6HXk+BVKZF+JcWjAsb/GEuq/eFdpuzSqeYTcfi6idkyugwfYwXFU1+5fZKUaRKYCwkkFQVfcAs1fXA5V+++FGfvjJ/CxURaSxaBvGdGDhfXE28LWuT9ozCl5xw4Yq5OGazvV24mZVSoOO0yZ31j7kYvtwYK6NeADwbSxDdJEqO4k//0zOHKrUiGYXtqw/A0LFFtqoZKFjnkCAQOjgdkwgdYwHQYDVR0OBBYEFMd9jMIhF1Ylmn/Tgt9r45jk14alMIGmBgNVHSMEgZ4wgZuAFMd9jMIhF1Ylmn/Tgt9r45jk14aloXikdjB0MQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEUMBIGA1UEChMLR29vZ2xlIEluYy4xEDAOBgNVBAsTB0FuZHJvaWQxEDAOBgNVBAMTB0FuZHJvaWSCCQDC4IdGZEowjTAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBBAUAA4IBAQBt0lLO74UwLDYKqs6Tm8/yzKkEu116FmH4rkaymUIE0P9KaMftGlMexFlaYjzmB2OxZyl6euNXEsQH8gjwyxCUKRJNexBiGcCEyj6z+a1fuHHvkiaai+KL8W1EyNmgjmyy8AW7P+LLlkR+ho5zEHatRbM/YAnqGcFh5iZBqpknHf1SKMXFh4dd239FJ1jWYfbMDMy3NS5CTMQ2XFI1MvcyUTdZPErjQfTbQe3aDQsQcafEQPD+nqActifKZ0Np0IS9L9kR/wbNvyz6ENwPiTrjV2KRkEjH78ZMcUQXg0L3BYHJ3lc69Vs5Ddf9uUGGMYldX3WfMBEmh/9iFBDAaTCK
        </item>
    </string-array></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values\preloaded_fonts.xml" qualifiers=""><array name="preloaded_fonts" translatable="false">
        <item>@font/abhaya_libre</item>
        <item>@font/abril_fatface</item>
        <item>@font/acme</item>
        <item>@font/adamina</item>
        <item>@font/advent_pro_medium</item>
        <item>@font/advent_pro_semibold</item>
        <item>@font/afacad_medium</item>
        <item>@font/agbalumo</item>
        <item>@font/aladin</item>
        <item>@font/alata</item>
        <item>@font/alatsi</item>
        <item>@font/aleo_medium_italic</item>
        <item>@font/alexandria</item>
        <item>@font/alice</item>
        <item>@font/alkatra</item>
        <item>@font/alkatra_medium</item>
        <item>@font/allerta</item>
        <item>@font/almendra</item>
        <item>@font/amaranth</item>
        <item>@font/amaranth_bold</item>
        <item>@font/andika_italic</item>
        <item>@font/anek_bangla_medium</item>
        <item>@font/archivo_black</item>
        <item>@font/artifika</item>

    </array></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Text Repeater</string><string name="ADMOB_APP_ID">ca-app-pub-8559393431170327~6652981751</string><string name="title_1">Text Repeater</string><string name="title_2">Rady-Made Short Messages</string><string name="title_3">Stylish Font</string><string name="description_1">Effortlessly duplicate your messages with just a few taps for messaging, social media posts, and more!</string><string name="description_2">Enhance your messaging experience with short messages.Here you find various messages,which you can use very easily,</string><string name="description_3">Explore a wide range of trendy fonts and give your messages a unique style.Use them for chats, social media posts,and more!</string><string name="main_title">Enhance your messaging with short messages,</string><string name="item_title1">Text Repeater</string><string name="short_message">Pre-Made Messages</string><string name="item_title2">Text to Emoji</string><string name="item_title3">Stylish Font</string><string name="item_title4">Emoji Art</string><string name="item_title5">Decoration Text</string><string name="item_title6">Random Text</string><string name="item_title7">Blank Text</string><string name="item_sub_title1">Just a simple tap to repeat text and emojis.</string><string name="item_sub_title2">Instantly convert your text into emoji.</string><string name="item_sub_title3">Explore a wide range of trendy fonts and style.</string><string name="item_sub_title4">You can use different types of emoji art.</string><string name="item_sub_title5">Convert your plain text into decorative text.</string><string name="item_sub_title6">Generate random words,letters,emoji and symbols.</string><string name="item_sub_title7">Create empty messages just a simple tap.</string><string name="messages_des">Enhance your messaging experience with short messages</string><string name="diolog_description">Welcome to Angina Tech.\nWe develop user-friendly and innovative apps to improve your digital experience. Your feedback matters, and we continuously work to enhance our apps.\n\nFor support or inquiries, feel free to contact us. Thank you for choosing Angina Tech.</string></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values\style.xml" qualifiers=""><style name="Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">20sp</item>
    </style><style name="CollapsedTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/black</item>
    </style><style name="ExpandedTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style><style name="MyTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:ellipsize">none</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style><style name="DialogTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">15sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:ellipsize">none</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">3dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">0.01</item>
    </style><style name="BottomSheetDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in</item>
        <item name="android:windowExitAnimation">@anim/slide_out</item>
    </style><style name="RobotoBoldTextAppearance">
        <item name="android:fontFamily">@font/arima_madurai_bold</item>
        <item name="android:textSize">23sp</item>

    </style></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.TextRepeater" parent="Theme.Material3.DayNight.NoActionBar">
        
        

    </style><style name="Theme.TextRepeater" parent="Base.Theme.TextRepeater"/></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="sub_layout_color">#23374D</color><color name="mother_layout_color">#062743</color><color name="primary_text">#E6FFFFFF</color><color name="love">#ff819f</color><color name="status_bar_light">#062743</color><color name="navigation_color">#23374D</color><color name="secondary_text">#8FFFFFFF</color><color name="toolbar_text_color">#D9FFFFFF</color><color name="copy_color">#2AE91E63</color><color name="view_color">#30FFFFFF</color><color name="emoji_view_color">#14FFFFFF</color></file><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.TextRepeater" parent="Theme.Material3.DayNight.NoActionBar">
        
        

    </style></file><file name="backup_rules" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="gma_ad_services_config" path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\res\xml\gma_ad_services_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\build\generated\res\resValues\release"/><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\build\generated\res\resValues\release"/><source path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\build\generated\res\processReleaseGoogleServices"><file path="C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">208219292648</string><string name="google_api_key" translatable="false">AIzaSyCg-aYYKF5kpJy44D32mOd0mE1kaQvFRhE</string><string name="google_app_id" translatable="false">1:208219292648:android:34d4abfb57be581a68da5d</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCg-aYYKF5kpJy44D32mOd0mE1kaQvFRhE</string><string name="google_storage_bucket" translatable="false">text-8df1a.firebasestorage.app</string><string name="project_id" translatable="false">text-8df1a</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>