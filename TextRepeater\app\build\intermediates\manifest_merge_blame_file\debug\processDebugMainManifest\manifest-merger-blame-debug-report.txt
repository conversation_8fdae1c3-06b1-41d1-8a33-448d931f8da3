1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anginatech.textrepeater"
4    android:versionCode="3"
5    android:versionName="3.9.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:5-76
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:22-73
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:5-66
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:5-78
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:5-82
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:22-79
17
18    <!-- Notification permission for Android 13+ (API 33+) -->
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:5-77
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:22-74
20
21    <supports-screens
21-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:15:5-18:11
22        android:anyDensity="true"
22-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:16:9-34
23        android:resizeable="true" />
23-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:17:9-34
24
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
28    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
29    <queries>
29-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
30
31        <!-- For browser content -->
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent>
39        <!-- End of browser content -->
40        <!-- For CustomTabsService -->
41        <intent>
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
42            <action android:name="android.support.customtabs.action.CustomTabsService" />
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
43        </intent>
44        <!-- End of CustomTabsService -->
45        <!-- For MRAID capabilities -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
47            <action android:name="android.intent.action.INSERT" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
48
49            <data android:mimeType="vnd.android.cursor.dir/event" />
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
50        </intent>
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
52            <action android:name="android.intent.action.VIEW" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
53
54            <data android:scheme="sms" />
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
57            <action android:name="android.intent.action.DIAL" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
58
59            <data android:path="tel:" />
59-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
60        </intent>
61        <!-- End of MRAID capabilities -->
62    </queries>
63
64    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
65    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
66
67    <permission
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
68        android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
72
73    <application
73-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:20:5-149:19
74        android:name="com.anginatech.textrepeater.MyApplication"
74-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:21:9-38
75        android:allowBackup="true"
75-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:22:9-35
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
77        android:dataExtractionRules="@xml/data_extraction_rules"
77-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:23:9-65
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:fullBackupContent="@xml/backup_rules"
80-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:24:9-54
81        android:icon="@mipmap/ic_launcher"
81-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:25:9-43
82        android:label="@string/app_name"
82-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:26:9-41
83        android:roundIcon="@mipmap/ic_launcher_round"
83-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:27:9-54
84        android:supportsRtl="true"
84-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:28:9-35
85        android:testOnly="true"
86        android:theme="@style/Theme.TextRepeater"
86-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:29:9-50
87        android:usesCleartextTraffic="true" >
87-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:30:9-44
88        <activity
88-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:32:9-37:15
89            android:name="com.anginatech.textrepeater.Decoration_Text_Activity"
89-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:33:13-53
90            android:configChanges="uiMode|screenSize|orientation"
90-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:35:13-66
91            android:exported="false"
91-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:34:13-37
92            android:windowSoftInputMode="adjustPan" />
92-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:36:13-52
93        <activity
93-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:38:9-43:15
94            android:name="com.anginatech.textrepeater.Stylish_Font_Activity"
94-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:39:13-50
95            android:configChanges="uiMode|screenSize|orientation"
95-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:40:13-66
96            android:exported="false"
96-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:41:13-37
97            android:windowSoftInputMode="adjustPan" />
97-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:42:13-52
98        <activity
98-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:44:9-49:15
99            android:name="com.anginatech.textrepeater.Text_to_Imoji_Activity"
99-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:45:13-51
100            android:configChanges="uiMode|screenSize|orientation"
100-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:46:13-66
101            android:exported="false"
101-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:47:13-37
102            android:windowSoftInputMode="adjustPan" />
102-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:48:13-52
103        <activity
103-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:50:9-55:15
104            android:name="com.anginatech.textrepeater.Blank_Text_Activity"
104-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:51:13-48
105            android:configChanges="uiMode|screenSize|orientation"
105-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:52:13-66
106            android:exported="false"
106-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:53:13-37
107            android:windowSoftInputMode="adjustPan" />
107-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:54:13-52
108        <activity
108-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:56:9-61:15
109            android:name="com.anginatech.textrepeater.Emoji_Art"
109-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:57:13-38
110            android:configChanges="uiMode|screenSize|orientation"
110-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:58:13-66
111            android:exported="false"
111-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:59:13-37
112            android:windowSoftInputMode="adjustPan" />
112-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:60:13-52
113        <activity
113-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:62:9-67:15
114            android:name="com.anginatech.textrepeater.Text_Repeat"
114-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:63:13-40
115            android:configChanges="uiMode|screenSize|orientation"
115-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:64:13-66
116            android:exported="false"
116-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:65:13-37
117            android:windowSoftInputMode="adjustPan" />
117-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:66:13-52
118        <activity
118-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:68:9-73:15
119            android:name="com.anginatech.textrepeater.Random_Text_Activity"
119-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:69:13-49
120            android:configChanges="uiMode|screenSize|orientation"
120-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:70:13-66
121            android:exported="false"
121-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:71:13-37
122            android:windowSoftInputMode="adjustPan" />
122-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:72:13-52
123        <activity
123-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:74:9-79:15
124            android:name="com.anginatech.textrepeater.Message_Activity"
124-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:75:13-45
125            android:configChanges="uiMode|screenSize|orientation"
125-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:76:13-66
126            android:exported="false"
126-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:77:13-37
127            android:windowSoftInputMode="adjustPan" />
127-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:78:13-52
128        <activity
128-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:80:9-85:15
129            android:name="com.anginatech.textrepeater.Settings_Activity"
129-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:81:13-46
130            android:configChanges="uiMode|screenSize|orientation"
130-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:82:13-66
131            android:exported="false"
131-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:83:13-37
132            android:windowSoftInputMode="adjustPan" />
132-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:84:13-52
133        <activity
133-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:86:9-91:15
134            android:name="com.anginatech.textrepeater.Navigation_Activity"
134-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:87:13-48
135            android:configChanges="uiMode|screenSize|orientation"
135-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:88:13-66
136            android:exported="false"
136-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:89:13-37
137            android:windowSoftInputMode="adjustPan" />
137-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:90:13-52
138        <activity
138-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:92:9-103:20
139            android:name="com.anginatech.textrepeater.Splash_Screen"
139-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:93:13-42
140            android:configChanges="uiMode|screenSize|orientation"
140-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:94:13-66
141            android:exported="true"
141-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:95:13-36
142            android:windowSoftInputMode="adjustPan" >
142-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:96:13-52
143            <intent-filter>
143-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:98:13-102:29
144                <action android:name="android.intent.action.MAIN" />
144-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:17-69
144-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:25-66
145
146                <category android:name="android.intent.category.LAUNCHER" />
146-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:17-77
146-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:27-74
147            </intent-filter>
148        </activity>
149        <activity
149-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:104:9-109:15
150            android:name="com.anginatech.textrepeater.MainActivity"
150-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:105:13-41
151            android:configChanges="uiMode|screenSize|orientation"
151-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:106:13-66
152            android:exported="true"
152-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:107:13-36
153            android:windowSoftInputMode="adjustPan" />
153-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:108:13-52
154
155        <meta-data
155-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:112:9-114:57
156            android:name="preloaded_fonts"
156-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:113:13-43
157            android:resource="@array/preloaded_fonts" />
157-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:114:13-54
158        <meta-data
158-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:115:9-117:51
159            android:name="com.google.android.gms.ads.APPLICATION_ID"
159-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:116:13-69
160            android:value="@string/ADMOB_APP_ID" />
160-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:117:13-49
161
162        <property
162-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:119:9-123:15
163            android:name="android.adservices.AD_SERVICES_CONFIG"
163-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:120:13-65
164            android:resource="@xml/gma_ad_services_config" />
164-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:121:13-59
165
166        <!-- Firebase Cloud Messaging Service -->
167        <service
167-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:126:9-132:19
168            android:name="com.anginatech.textrepeater.MyFirebaseMessagingService"
168-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:127:13-55
169            android:exported="false" >
169-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:128:13-37
170            <intent-filter>
170-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
171                <action android:name="com.google.firebase.MESSAGING_EVENT" />
171-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
171-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
172            </intent-filter>
173        </service>
174
175        <!-- Firebase Cloud Messaging default notification icon -->
176        <meta-data
176-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:135:9-137:67
177            android:name="com.google.firebase.messaging.default_notification_icon"
177-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:136:13-83
178            android:resource="@drawable/ic_launcher_foreground" />
178-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:137:13-64
179
180        <!-- Firebase Cloud Messaging default notification color -->
181        <meta-data
181-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:140:9-142:46
182            android:name="com.google.firebase.messaging.default_notification_color"
182-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:141:13-84
183            android:resource="@color/love" />
183-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:142:13-43
184
185        <!-- Firebase Cloud Messaging default notification channel -->
186        <meta-data
186-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:145:9-147:59
187            android:name="com.google.firebase.messaging.default_notification_channel_id"
187-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:146:13-89
188            android:value="text_repeater_notifications" />
188-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:147:13-56
189
190        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
191        <activity
191-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
192            android:name="com.google.android.gms.ads.AdActivity"
192-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
193            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
193-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
194            android:exported="false"
194-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
195            android:theme="@android:style/Theme.Translucent" />
195-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
196
197        <provider
197-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
198            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
198-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
199            android:authorities="com.anginatech.textrepeater.mobileadsinitprovider"
199-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
200            android:exported="false"
200-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
201            android:initOrder="100" />
201-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
202
203        <service
203-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
204            android:name="com.google.android.gms.ads.AdService"
204-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
205            android:enabled="true"
205-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
206            android:exported="false" />
206-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
207
208        <activity
208-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
209            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
209-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
210            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
210-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
211            android:exported="false" />
211-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
212        <activity
212-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
213            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
213-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
214            android:excludeFromRecents="true"
214-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
215            android:exported="false"
215-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
216            android:launchMode="singleTask"
216-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
217            android:taskAffinity=""
217-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
218            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
218-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
219
220        <meta-data
220-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
221            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
221-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
222            android:value="true" />
222-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
223        <meta-data
223-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
224            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
224-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
225            android:value="true" />
225-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
226
227        <receiver
227-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
228            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
228-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
229            android:exported="true"
229-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
230            android:permission="com.google.android.c2dm.permission.SEND" >
230-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
231            <intent-filter>
231-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
232                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
232-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
232-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
233            </intent-filter>
234
235            <meta-data
235-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
236                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
236-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
237                android:value="true" />
237-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
238        </receiver>
239        <!--
240             FirebaseMessagingService performs security checks at runtime,
241             but set to not exported to explicitly avoid allowing another app to call it.
242        -->
243        <service
243-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
244            android:name="com.google.firebase.messaging.FirebaseMessagingService"
244-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
245            android:directBootAware="true"
245-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
246            android:exported="false" >
246-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
247            <intent-filter android:priority="-500" >
247-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
248                <action android:name="com.google.firebase.MESSAGING_EVENT" />
248-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
248-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
249            </intent-filter>
250        </service>
251        <service
251-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
252            android:name="com.google.firebase.components.ComponentDiscoveryService"
252-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
253            android:directBootAware="true"
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
254            android:exported="false" >
254-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
255            <meta-data
255-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
256                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
256-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
258            <meta-data
258-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
259                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
259-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
261            <meta-data
261-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
262                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
262-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
264            <meta-data
264-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
265                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
265-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
267            <meta-data
267-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
268                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
268-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
270            <meta-data
270-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
271                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
271-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
272                android:value="com.google.firebase.components.ComponentRegistrar" />
272-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
273            <meta-data
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
274                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
276            <meta-data
276-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
277                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
277-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
279        </service>
280
281        <provider
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
282            android:name="com.google.firebase.provider.FirebaseInitProvider"
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
283            android:authorities="com.anginatech.textrepeater.firebaseinitprovider"
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
284            android:directBootAware="true"
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
285            android:exported="false"
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
286            android:initOrder="100" />
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
287
288        <receiver
288-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
289            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
289-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
290            android:enabled="true"
290-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
291            android:exported="false" >
291-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
292        </receiver>
293
294        <service
294-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
295            android:name="com.google.android.gms.measurement.AppMeasurementService"
295-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
296            android:enabled="true"
296-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
297            android:exported="false" />
297-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
298        <service
298-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
299            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
299-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
300            android:enabled="true"
300-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
301            android:exported="false"
301-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
302            android:permission="android.permission.BIND_JOB_SERVICE" />
302-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
303
304        <provider
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
305            android:name="androidx.startup.InitializationProvider"
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
306            android:authorities="com.anginatech.textrepeater.androidx-startup"
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
307            android:exported="false" >
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
308            <meta-data
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
309                android:name="androidx.work.WorkManagerInitializer"
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
310                android:value="androidx.startup" />
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
311            <meta-data
311-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
312                android:name="androidx.emoji2.text.EmojiCompatInitializer"
312-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
313                android:value="androidx.startup" />
313-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
314            <meta-data
314-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
315                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
315-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
316                android:value="androidx.startup" />
316-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
317            <meta-data
317-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
318                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
318-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
319                android:value="androidx.startup" />
319-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
320        </provider>
321
322        <service
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
323            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
324            android:directBootAware="false"
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
325            android:enabled="@bool/enable_system_alarm_service_default"
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
326            android:exported="false" />
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
327        <service
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
328            android:name="androidx.work.impl.background.systemjob.SystemJobService"
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
330            android:enabled="@bool/enable_system_job_service_default"
330-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
331            android:exported="true"
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
332            android:permission="android.permission.BIND_JOB_SERVICE" />
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
333        <service
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
334            android:name="androidx.work.impl.foreground.SystemForegroundService"
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
335            android:directBootAware="false"
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
336            android:enabled="@bool/enable_system_foreground_service_default"
336-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
337            android:exported="false" />
337-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
338
339        <receiver
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
340            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
341            android:directBootAware="false"
341-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
342            android:enabled="true"
342-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
343            android:exported="false" />
343-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
344        <receiver
344-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
345            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
346            android:directBootAware="false"
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
347            android:enabled="false"
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
348            android:exported="false" >
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
349            <intent-filter>
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
350                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
350-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
350-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
351                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
351-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
351-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
352            </intent-filter>
353        </receiver>
354        <receiver
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
355            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
356            android:directBootAware="false"
356-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
357            android:enabled="false"
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
358            android:exported="false" >
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
359            <intent-filter>
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
360                <action android:name="android.intent.action.BATTERY_OKAY" />
360-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
360-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
361                <action android:name="android.intent.action.BATTERY_LOW" />
361-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
361-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
362            </intent-filter>
363        </receiver>
364        <receiver
364-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
365            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
366            android:directBootAware="false"
366-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
367            android:enabled="false"
367-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
368            android:exported="false" >
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
369            <intent-filter>
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
370                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
370-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
370-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
371                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
371-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
371-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
372            </intent-filter>
373        </receiver>
374        <receiver
374-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
375            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
375-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
376            android:directBootAware="false"
376-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
377            android:enabled="false"
377-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
378            android:exported="false" >
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
379            <intent-filter>
379-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
380                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
380-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
380-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
381            </intent-filter>
382        </receiver>
383        <receiver
383-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
384            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
385            android:directBootAware="false"
385-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
386            android:enabled="false"
386-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
387            android:exported="false" >
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
388            <intent-filter>
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
389                <action android:name="android.intent.action.BOOT_COMPLETED" />
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
390                <action android:name="android.intent.action.TIME_SET" />
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
391                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
392            </intent-filter>
393        </receiver>
394        <receiver
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
395            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
396            android:directBootAware="false"
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
397            android:enabled="@bool/enable_system_alarm_service_default"
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
398            android:exported="false" >
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
399            <intent-filter>
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
400                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
401            </intent-filter>
402        </receiver>
403        <receiver
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
404            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
405            android:directBootAware="false"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
406            android:enabled="true"
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
407            android:exported="true"
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
408            android:permission="android.permission.DUMP" >
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
409            <intent-filter>
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
410                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
411            </intent-filter>
412        </receiver>
413
414        <activity
414-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
415            android:name="com.google.android.gms.common.api.GoogleApiActivity"
415-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
416            android:exported="false"
416-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
417            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
417-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
418
419        <uses-library
419-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
420            android:name="android.ext.adservices"
420-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
421            android:required="false" />
421-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
422
423        <service
423-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
424            android:name="androidx.room.MultiInstanceInvalidationService"
424-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
425            android:directBootAware="true"
425-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
426            android:exported="false" />
426-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
427
428        <meta-data
428-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
429            android:name="com.google.android.gms.version"
429-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
430            android:value="@integer/google_play_services_version" />
430-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
431
432        <service
432-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
433            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
433-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
434            android:exported="false" >
434-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
435            <meta-data
435-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
436                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
436-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
437                android:value="cct" />
437-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
438        </service>
439        <service
439-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
440            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
440-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
441            android:exported="false"
441-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
442            android:permission="android.permission.BIND_JOB_SERVICE" >
442-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
443        </service>
444
445        <receiver
445-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
446            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
446-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
447            android:exported="false" />
447-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
448        <receiver
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
449            android:name="androidx.profileinstaller.ProfileInstallReceiver"
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
450            android:directBootAware="false"
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
451            android:enabled="true"
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
452            android:exported="true"
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
453            android:permission="android.permission.DUMP" >
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
454            <intent-filter>
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
455                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
456            </intent-filter>
457            <intent-filter>
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
458                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
459            </intent-filter>
460            <intent-filter>
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
461                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
462            </intent-filter>
463            <intent-filter>
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
464                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
464-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
464-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
465            </intent-filter>
466        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
467        <activity
467-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
468            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
468-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
469            android:exported="false"
469-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
470            android:stateNotNeeded="true"
470-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
471            android:theme="@style/Theme.PlayCore.Transparent" />
471-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
472    </application>
473
474</manifest>
