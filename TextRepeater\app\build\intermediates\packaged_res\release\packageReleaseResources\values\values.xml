<?xml version="1.0" encoding="utf-8"?>
<resources>
    <array name="com_google_android_gms_fonts_certs">
        <item>@array/com_google_android_gms_fonts_certs_dev</item>
        <item>@array/com_google_android_gms_fonts_certs_prod</item>
    </array>
    <string-array name="com_google_android_gms_fonts_certs_dev">
        <item>
            MIIEqDCCA5CgAwIBAgIJANWFuGx90071MA0GCSqGSIb3DQEBBAUAMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTAeFw0wODA0MTUyMzM2NTZaFw0zNTA5MDEyMzM2NTZaMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbTCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBANbOLggKv+IxTdGNs8/TGFy0PTP6DHThvbbR24kT9ixcOd9W+EaBPWW+wPPKQmsHxajtWjmQwWfna8mZuSeJS48LIgAZlKkpFeVyxW0qMBujb8X8ETrWy550NaFtI6t9+u7hZeTfHwqNvacKhp1RbE6dBRGWynwMVX8XW8N1+UjFaq6GCJukT4qmpN2afb8sCjUigq0GuMwYXrFVee74bQgLHWGJwPmvmLHC69EH6kWr22ijx4OKXlSIx2xT1AsSHee70w5iDBiK4aph27yH3TxkXy9V89TDdexAcKk/cVHYNnDBapcavl7y0RiQ4biu8ymM8Ga/nmzhRKya6G0cGw8CAQOjgfwwgfkwHQYDVR0OBBYEFI0cxb6VTEM8YYY6FbBMvAPyT+CyMIHJBgNVHSMEgcEwgb6AFI0cxb6VTEM8YYY6FbBMvAPyT+CyoYGapIGXMIGUMQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEQMA4GA1UEChMHQW5kcm9pZDEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDEiMCAGCSqGSIb3DQEJARYTYW5kcm9pZEBhbmRyb2lkLmNvbYIJANWFuGx90071MAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQEEBQADggEBABnTDPEF+3iSP0wNfdIjIz1AlnrPzgAIHVvXxunW7SBrDhEglQZBbKJEk5kT0mtKoOD1JMrSu1xuTKEBahWRbqHsXclaXjoBADb0kkjVEJu/Lh5hgYZnOjvlba8Ld7HCKePCVePoTJBdI4fvugnL8TsgK05aIskyY0hKI9L8KfqfGTl1lzOv2KoWD0KWwtAWPoGChZxmQ+nBli+gwYMzM1vAkP+aayLe0a1EQimlOalO762r0GXO0ks+UeXde2Z4e+8S/pf7pITEI/tP+MxJTALw9QUWEv9lKTk+jkbqxbsh8nfBUapfKqYn0eidpwq2AzVp3juYl7//fKnaPhJD9gs=
        </item>
    </string-array>
    <string-array name="com_google_android_gms_fonts_certs_prod">
        <item>
            MIIEQzCCAyugAwIBAgIJAMLgh0ZkSjCNMA0GCSqGSIb3DQEBBAUAMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDAeFw0wODA4MjEyMzEzMzRaFw0zNjAxMDcyMzEzMzRaMHQxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpDYWxpZm9ybmlhMRYwFAYDVQQHEw1Nb3VudGFpbiBWaWV3MRQwEgYDVQQKEwtHb29nbGUgSW5jLjEQMA4GA1UECxMHQW5kcm9pZDEQMA4GA1UEAxMHQW5kcm9pZDCCASAwDQYJKoZIhvcNAQEBBQADggENADCCAQgCggEBAKtWLgDYO6IIrgqWbxJOKdoR8qtW0I9Y4sypEwPpt1TTcvZApxsdyxMJZ2JORland2qSGT2y5b+3JKkedxiLDmpHpDsz2WCbdxgxRczfey5YZnTJ4VZbH0xqWVW/8lGmPav5xVwnIiJS6HXk+BVKZF+JcWjAsb/GEuq/eFdpuzSqeYTcfi6idkyugwfYwXFU1+5fZKUaRKYCwkkFQVfcAs1fXA5V+++FGfvjJ/CxURaSxaBvGdGDhfXE28LWuT9ozCl5xw4Yq5OGazvV24mZVSoOO0yZ31j7kYvtwYK6NeADwbSxDdJEqO4k//0zOHKrUiGYXtqw/A0LFFtqoZKFjnkCAQOjgdkwgdYwHQYDVR0OBBYEFMd9jMIhF1Ylmn/Tgt9r45jk14alMIGmBgNVHSMEgZ4wgZuAFMd9jMIhF1Ylmn/Tgt9r45jk14aloXikdjB0MQswCQYDVQQGEwJVUzETMBEGA1UECBMKQ2FsaWZvcm5pYTEWMBQGA1UEBxMNTW91bnRhaW4gVmlldzEUMBIGA1UEChMLR29vZ2xlIEluYy4xEDAOBgNVBAsTB0FuZHJvaWQxEDAOBgNVBAMTB0FuZHJvaWSCCQDC4IdGZEowjTAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBBAUAA4IBAQBt0lLO74UwLDYKqs6Tm8/yzKkEu116FmH4rkaymUIE0P9KaMftGlMexFlaYjzmB2OxZyl6euNXEsQH8gjwyxCUKRJNexBiGcCEyj6z+a1fuHHvkiaai+KL8W1EyNmgjmyy8AW7P+LLlkR+ho5zEHatRbM/YAnqGcFh5iZBqpknHf1SKMXFh4dd239FJ1jWYfbMDMy3NS5CTMQ2XFI1MvcyUTdZPErjQfTbQe3aDQsQcafEQPD+nqActifKZ0Np0IS9L9kR/wbNvyz6ENwPiTrjV2KRkEjH78ZMcUQXg0L3BYHJ3lc69Vs5Ddf9uUGGMYldX3WfMBEmh/9iFBDAaTCK
        </item>
    </string-array>
    <array name="preloaded_fonts" translatable="false">
        <item>@font/abhaya_libre</item>
        <item>@font/abril_fatface</item>
        <item>@font/acme</item>
        <item>@font/adamina</item>
        <item>@font/advent_pro_medium</item>
        <item>@font/advent_pro_semibold</item>
        <item>@font/afacad_medium</item>
        <item>@font/agbalumo</item>
        <item>@font/aladin</item>
        <item>@font/alata</item>
        <item>@font/alatsi</item>
        <item>@font/aleo_medium_italic</item>
        <item>@font/alexandria</item>
        <item>@font/alice</item>
        <item>@font/alkatra</item>
        <item>@font/alkatra_medium</item>
        <item>@font/allerta</item>
        <item>@font/almendra</item>
        <item>@font/amaranth</item>
        <item>@font/amaranth_bold</item>
        <item>@font/andika_italic</item>
        <item>@font/anek_bangla_medium</item>
        <item>@font/archivo_black</item>
        <item>@font/artifika</item>

    </array>
    <color name="black">#FF000000</color>
    <color name="copy_color">#0FE91E63</color>
    <color name="emoji_view_color">#14000000</color>
    <color name="love">#ff819f</color>
    <color name="mother_layout_color">#FFFFFFFF</color>
    <color name="navigation_color">#EEEEEE</color>
    <color name="primary_text">#E6000000</color>
    <color name="secondary_text">#8F000000</color>
    <color name="status_bar_light">#FFFFFFFF</color>
    <color name="sub_layout_color">#EEEEEE</color>
    <color name="toolbar_text_color">#B8000000</color>
    <color name="view_color">#31000000</color>
    <color name="white">#FFFFFFFF</color>
    <string name="ADMOB_APP_ID">ca-app-pub-8559393431170327~6652981751</string>
    <string name="app_name">Text Repeater</string>
    <string name="description_1">Effortlessly duplicate your messages with just a few taps for messaging, social media posts, and more!</string>
    <string name="description_2">Enhance your messaging experience with short messages.Here you find various messages,which you can use very easily,</string>
    <string name="description_3">Explore a wide range of trendy fonts and give your messages a unique style.Use them for chats, social media posts,and more!</string>
    <string name="diolog_description">Welcome to Angina Tech.\nWe develop user-friendly and innovative apps to improve your digital experience. Your feedback matters, and we continuously work to enhance our apps.\n\nFor support or inquiries, feel free to contact us. Thank you for choosing Angina Tech.</string>
    <string name="gcm_defaultSenderId" translatable="false">208219292648</string>
    <string name="google_api_key" translatable="false">AIzaSyCg-aYYKF5kpJy44D32mOd0mE1kaQvFRhE</string>
    <string name="google_app_id" translatable="false">1:208219292648:android:34d4abfb57be581a68da5d</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyCg-aYYKF5kpJy44D32mOd0mE1kaQvFRhE</string>
    <string name="google_storage_bucket" translatable="false">text-8df1a.firebasestorage.app</string>
    <string name="item_sub_title1">Just a simple tap to repeat text and emojis.</string>
    <string name="item_sub_title2">Instantly convert your text into emoji.</string>
    <string name="item_sub_title3">Explore a wide range of trendy fonts and style.</string>
    <string name="item_sub_title4">You can use different types of emoji art.</string>
    <string name="item_sub_title5">Convert your plain text into decorative text.</string>
    <string name="item_sub_title6">Generate random words,letters,emoji and symbols.</string>
    <string name="item_sub_title7">Create empty messages just a simple tap.</string>
    <string name="item_title1">Text Repeater</string>
    <string name="item_title2">Text to Emoji</string>
    <string name="item_title3">Stylish Font</string>
    <string name="item_title4">Emoji Art</string>
    <string name="item_title5">Decoration Text</string>
    <string name="item_title6">Random Text</string>
    <string name="item_title7">Blank Text</string>
    <string name="main_title">Enhance your messaging with short messages,</string>
    <string name="messages_des">Enhance your messaging experience with short messages</string>
    <string name="project_id" translatable="false">text-8df1a</string>
    <string name="short_message">Pre-Made Messages</string>
    <string name="title_1">Text Repeater</string>
    <string name="title_2">Rady-Made Short Messages</string>
    <string name="title_3">Stylish Font</string>
    <style name="Base.Theme.TextRepeater" parent="Theme.Material3.DayNight.NoActionBar">
        
        

    </style>
    <style name="BottomSheetDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in</item>
        <item name="android:windowExitAnimation">@anim/slide_out</item>
    </style>
    <style name="CollapsedTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/black</item>
    </style>
    <style name="DialogTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">15sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:ellipsize">none</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">3dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">0.01</item>
    </style>
    <style name="ExpandedTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style>
    <style name="MyTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:ellipsize">none</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>
    <style name="RobotoBoldTextAppearance">
        <item name="android:fontFamily">@font/arima_madurai_bold</item>
        <item name="android:textSize">23sp</item>

    </style>
    <style name="Theme.TextRepeater" parent="Base.Theme.TextRepeater"/>
    <style name="Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">20sp</item>
    </style>
</resources>