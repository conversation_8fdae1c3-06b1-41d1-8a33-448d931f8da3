{"logs": [{"outputFile": "com.anginatech.textrepeater.app-mergeReleaseResources-55:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\xampp\\htdocs\\monirulvitextrepeater\\TextRepeater\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "6", "endColumns": "12", "endOffsets": "320"}, "to": {"startLines": "15", "startColumns": "4", "startOffsets": "687", "endLines": "19", "endColumns": "12", "endOffsets": "803"}}, {"source": "C:\\xampp\\htdocs\\monirulvitextrepeater\\TextRepeater\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "3,13,15,8,6,10,7,11,9,5,12,14,4", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "59,554,650,302,197,394,252,446,342,145,498,602,102", "endColumns": "41,46,52,38,53,50,48,50,50,50,54,46,41", "endOffsets": "96,596,698,336,246,440,296,492,388,191,548,644,139"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,97,144,197,236,290,341,390,441,492,543,598,645", "endColumns": "41,46,52,38,53,50,48,50,50,50,54,46,41", "endOffsets": "92,139,192,231,285,336,385,436,487,538,593,640,682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\a6be65feae4b33c1c7fd18a0c0ae1d8a\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "20,21,22,23,24,25,26,53", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "808,878,962,1046,1142,1244,1346,4328", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "873,957,1041,1137,1239,1341,1435,4412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bc7f51ca89ffc0f741ddbaa1d183190a\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1440,1515,1626,1715,1816,1923,2030,2129,2236,2339,2466,2554,2678,2780,2882,2998,3100,3214,3342,3458,3580,3716,3836,3970,4090,4202,4417,4534,4658,4788,4910,5048,5182,5298", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1510,1621,1710,1811,1918,2025,2124,2231,2334,2461,2549,2673,2775,2877,2993,3095,3209,3337,3453,3575,3711,3831,3965,4085,4197,4323,4529,4653,4783,4905,5043,5177,5293,5413"}}]}]}