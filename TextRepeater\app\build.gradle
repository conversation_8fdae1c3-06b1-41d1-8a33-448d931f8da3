plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services'

}

android {
    namespace 'com.anginatech.textrepeater'
    compileSdk 35

    defaultConfig {
        applicationId "com.anginatech.textrepeater"
        minSdk 23
        targetSdk 35
        versionCode 3
        versionName "3.9.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    android {
        compileOptions {
            encoding "UTF-8"
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.activity:activity:1.10.1'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    //Modern Performance Libraries------------------------------------------

    // Modern Networking (Replace Volley)
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Room Database for Modern Local Storage
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-rxjava3:2.6.1'
    annotationProcessor 'androidx.room:room-compiler:2.6.1'

    // RxJava for reactive database operations
    implementation 'io.reactivex.rxjava3:rxjava:3.1.8'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'

    // Coroutines for Async Operations
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'

    // ViewModel and LiveData
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.9.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.9.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.9.0'

    // WorkManager for Background Tasks
    implementation 'androidx.work:work-runtime:2.9.0'

    // Paging 3 for Large Data Sets
    implementation 'androidx.paging:paging-runtime:3.2.1'

    // Image Loading and Caching
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // Shimmer for Loading Effects
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    // Legacy Libraries (Keep for compatibility during migration)
    implementation 'com.android.volley:volley:1.2.1'

    // Firebase and Ads
    implementation platform('com.google.firebase:firebase-bom:33.14.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation("com.google.android.ump:user-messaging-platform:3.2.0")
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.gms:play-services-ads:24.3.0'

}