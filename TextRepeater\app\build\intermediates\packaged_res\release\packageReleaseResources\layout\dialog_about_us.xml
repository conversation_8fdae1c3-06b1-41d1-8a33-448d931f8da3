<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/mother_layout_color"
    android:padding="10dp"
    android:layout_marginBottom="20dp"
    >


    <ImageView
        android:id="@+id/imageView9"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        android:src="@drawable/angina_logo"
        />

    <TextView
        android:id="@+id/textView6"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:fontFamily="serif"
        android:lineSpacingExtra="5dp"
        android:text="@string/diolog_description"
        android:textColor="@color/secondary_text"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView9"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/textView8"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="28dp"
        android:fontFamily="serif"
        android:text="Contact Us :"
        android:textColor="@color/primary_text"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView6"
        app:layout_constraintVertical_bias="0.0" />


    <TextView
        android:id="@+id/textView_Email"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="2dp"
        android:fontFamily="serif"
        android:text="Email : <EMAIL>"
        android:textColor="@color/secondary_text"
        android:textSize="15sp"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.027"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView8"
        app:layout_constraintVertical_bias="0.042" />

    <TextView
        android:id="@+id/textView_Website"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="4dp"
        android:fontFamily="serif"
        android:text="Website : www.anginatech.com"
        android:textColor="@color/secondary_text"
        android:textSize="15sp"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView_Email"
        app:layout_constraintVertical_bias="0.0" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/dialog_Dismis_Button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        android:layout_marginTop="26dp"
        android:text="Close"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        android:elevation="0dp"
        android:textSize="15sp"
        android:backgroundTint="@color/love"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textView_Website"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        />


</androidx.constraintlayout.widget.ConstraintLayout>