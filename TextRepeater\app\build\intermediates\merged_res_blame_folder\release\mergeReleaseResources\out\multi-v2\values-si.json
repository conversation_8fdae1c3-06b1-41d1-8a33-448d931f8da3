{"logs": [{"outputFile": "com.anginatech.textrepeater.app-mergeReleaseResources-55:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\131f10e519d4cb41eac2bb9322c4fa1c\\transformed\\play-services-ads-24.3.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,348,414,482,578,643,755,849,956,1007,1067,1183,1275,1316,1404,1440,1485,1537,1616,1664", "endColumns": "45,48,53,65,67,95,64,111,93,106,50,59,115,91,40,87,35,44,51,78,47,55", "endOffsets": "244,293,347,413,481,577,642,754,848,955,1006,1066,1182,1274,1315,1403,1439,1484,1536,1615,1663,1719"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,146,148,149,150,151,152,153,154,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12020,12070,12123,12181,12251,12323,12423,12492,12608,12706,12817,12872,12936,13056,13232,13277,13369,13409,13458,13514,13597,14068", "endColumns": "49,52,57,69,71,99,68,115,97,110,54,63,119,95,44,91,39,48,55,82,51,59", "endOffsets": "12065,12118,12176,12246,12318,12418,12487,12603,12701,12812,12867,12931,13051,13147,13272,13364,13404,13453,13509,13592,13644,14123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bc7f51ca89ffc0f741ddbaa1d183190a\\transformed\\material-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1037,1101,1190,1257,1317,1411,1475,1538,1594,1664,1731,1786,1905,1962,2026,2080,2153,2275,2358,2441,2534,2620,2705,2837,2915,2995,3117,3203,3287,3347,3399,3465,3535,3608,3679,3756,3828,3905,3977,4047,4160,4253,4326,4416,4509,4583,4655,4746,4800,4880,4946,5030,5115,5177,5241,5304,5370,5475,5580,5675,5776,5840,5896,5976,6061,6136", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "264,340,417,495,586,671,773,888,971,1032,1096,1185,1252,1312,1406,1470,1533,1589,1659,1726,1781,1900,1957,2021,2075,2148,2270,2353,2436,2529,2615,2700,2832,2910,2990,3112,3198,3282,3342,3394,3460,3530,3603,3674,3751,3823,3900,3972,4042,4155,4248,4321,4411,4504,4578,4650,4741,4795,4875,4941,5025,5110,5172,5236,5299,5365,5470,5575,5670,5771,5835,5891,5971,6056,6131,6207"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3035,3111,3188,3266,3357,4174,4276,4391,6772,6833,6897,7314,7381,7441,7535,7599,7662,7718,7788,7855,7910,8029,8086,8150,8204,8277,8399,8482,8565,8658,8744,8829,8961,9039,9119,9241,9327,9411,9471,9523,9589,9659,9732,9803,9880,9952,10029,10101,10171,10284,10377,10450,10540,10633,10707,10779,10870,10924,11004,11070,11154,11239,11301,11365,11428,11494,11599,11704,11799,11900,11964,13152,13731,13816,13891", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "314,3106,3183,3261,3352,3437,4271,4386,4469,6828,6892,6981,7376,7436,7530,7594,7657,7713,7783,7850,7905,8024,8081,8145,8199,8272,8394,8477,8560,8653,8739,8824,8956,9034,9114,9236,9322,9406,9466,9518,9584,9654,9727,9798,9875,9947,10024,10096,10166,10279,10372,10445,10535,10628,10702,10774,10865,10919,10999,11065,11149,11234,11296,11360,11423,11489,11594,11699,11794,11895,11959,12015,13227,13811,13886,13962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6a607bbfcc83cfb280a4c2f3d8ba8b9e\\transformed\\play-services-basement-18.5.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5453", "endColumns": "138", "endOffsets": "5587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\446fe71953247653d35c77c031840418\\transformed\\browser-1.8.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6664,6986,7093,7209", "endColumns": "107,106,115,104", "endOffsets": "6767,7088,7204,7309"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\917d0d7e0c096b9e28e4e727a3aa98ed\\transformed\\play-services-base-18.5.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4474,4582,4736,4860,4973,5115,5239,5355,5592,5743,5858,6014,6145,6289,6450,6523,6584", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "4577,4731,4855,4968,5110,5234,5350,5448,5738,5853,6009,6140,6284,6445,6518,6579,6659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c778fdfe6002d7eef6060ec9b8f3c394\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3442,3544,3647,3752,3857,3956,4060,13967", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3539,3642,3747,3852,3951,4055,4169,14063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\a6be65feae4b33c1c7fd18a0c0ae1d8a\\transformed\\appcompat-1.7.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,435,542,649,732,837,953,1043,1129,1220,1313,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2362,2460,2570,2670,2777,2936,13649", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "430,537,644,727,832,948,1038,1124,1215,1308,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2357,2455,2565,2665,2772,2931,3030,13726"}}]}]}