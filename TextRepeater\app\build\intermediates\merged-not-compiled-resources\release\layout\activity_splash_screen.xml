<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Splash_Screen"
    android:background="@color/love"
    android:id="@+id/main"
    >

    <ImageView
        android:id="@+id/imageView2"
        android:layout_width="130dp"
        android:layout_height="130dp"
        android:src="@drawable/logo"
        app:layout_constraintBottom_toTopOf="@+id/loadingContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        />

    <!-- Enhanced Loading Container for Open Ads -->
    <LinearLayout
        android:id="@+id/loadingContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginTop="40dp"
        android:visibility="gone"
        android:padding="24dp"
        android:background="@drawable/loading_background"
        app:layout_constraintTop_toBottomOf="@+id/imageView2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Animated Progress Container -->
        <RelativeLayout
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginBottom="20dp">

            <!-- Outer Progress Ring -->
            <ProgressBar
                android:id="@+id/loadingProgressBar"
                style="?android:attr/progressBarStyleLarge"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_centerInParent="true"
                android:indeterminateTint="@android:color/white"
                android:indeterminateTintMode="src_atop" />

            <!-- Inner Pulsing Dot -->
            <View
                android:id="@+id/pulsingDot"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_centerInParent="true"
                android:background="@drawable/pulsing_dot"
                android:alpha="0.8" />

        </RelativeLayout>

        <!-- Loading Text with Animation -->
        <TextView
            android:id="@+id/loadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Loading ads..."
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:fontFamily="sans-serif-medium"
            android:alpha="0.95"
            android:layout_marginBottom="8dp"
            android:textAlignment="center" />

        <!-- Loading Sub Text -->
        <TextView
            android:id="@+id/loadingSubText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Please wait while we prepare your experience"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            android:layout_marginTop="4dp"
            android:alpha="0.75"
            android:gravity="center"
            android:textAlignment="center"
            android:maxWidth="250dp" />

        <!-- Progress Percentage (Optional) -->
        <TextView
            android:id="@+id/loadingPercentage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="@android:color/white"
            android:textSize="11sp"
            android:layout_marginTop="12dp"
            android:alpha="0.6"
            android:visibility="gone"
            android:textAlignment="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>