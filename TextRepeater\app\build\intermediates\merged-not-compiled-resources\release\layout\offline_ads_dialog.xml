
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="40dp"
    android:paddingHorizontal="24dp"
    android:background="@drawable/offline_dialog_background"
    android:orientation="vertical"
    android:filterTouchesWhenObscured="true">
    <ImageView
        android:id="@+id/offline_dialog_image"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_gravity="center"
        android:contentDescription="@string/offline_dialog_image_description"
        android:scaleType="centerInside"
        android:src="@drawable/offline_dialog_default_icon_42dp" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="14dp"
        android:orientation="vertical">
        <TextView
            android:id="@+id/offline_dialog_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/offline_dialog_text"
            android:textColor="@android:color/black" />
        <TextView
            android:id="@id/offline_dialog_advertiser_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="12dp"
            android:visibility="gone"
            android:gravity="center"
            android:text=""
            android:textColor="@android:color/darker_gray" />
    </LinearLayout>
</LinearLayout>
