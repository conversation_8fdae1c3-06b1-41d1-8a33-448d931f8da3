1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anginatech.textrepeater"
4    android:versionCode="3"
5    android:versionName="3.9.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:5-76
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:22-73
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:5-66
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:5-78
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:5-82
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:22-79
17
18    <!-- Notification permission for Android 13+ (API 33+) -->
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:5-77
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:22-74
20
21    <supports-screens
21-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:15:5-18:11
22        android:anyDensity="true"
22-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:16:9-34
23        android:resizeable="true" />
23-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:17:9-34
24
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
28    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
29    <queries>
29-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
30
31        <!-- For browser content -->
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent>
39        <!-- End of browser content -->
40        <!-- For CustomTabsService -->
41        <intent>
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
42            <action android:name="android.support.customtabs.action.CustomTabsService" />
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
43        </intent>
44        <!-- End of CustomTabsService -->
45        <!-- For MRAID capabilities -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
47            <action android:name="android.intent.action.INSERT" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
48
49            <data android:mimeType="vnd.android.cursor.dir/event" />
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
50        </intent>
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
52            <action android:name="android.intent.action.VIEW" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
53
54            <data android:scheme="sms" />
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
57            <action android:name="android.intent.action.DIAL" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
58
59            <data android:path="tel:" />
59-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
60        </intent>
61        <!-- End of MRAID capabilities -->
62    </queries>
63
64    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
65    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
66
67    <permission
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
68        android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
72
73    <application
73-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:20:5-149:19
74        android:name="com.anginatech.textrepeater.MyApplication"
74-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:21:9-38
75        android:allowBackup="true"
75-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:22:9-35
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c778fdfe6002d7eef6060ec9b8f3c394\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
77        android:dataExtractionRules="@xml/data_extraction_rules"
77-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:23:9-65
78        android:extractNativeLibs="false"
79        android:fullBackupContent="@xml/backup_rules"
79-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:24:9-54
80        android:icon="@mipmap/ic_launcher"
80-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:25:9-43
81        android:label="@string/app_name"
81-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:26:9-41
82        android:roundIcon="@mipmap/ic_launcher_round"
82-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:27:9-54
83        android:supportsRtl="true"
83-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:28:9-35
84        android:theme="@style/Theme.TextRepeater"
84-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:29:9-50
85        android:usesCleartextTraffic="true" >
85-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:30:9-44
86        <activity
86-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:32:9-37:15
87            android:name="com.anginatech.textrepeater.Decoration_Text_Activity"
87-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:33:13-53
88            android:configChanges="uiMode|screenSize|orientation"
88-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:35:13-66
89            android:exported="false"
89-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:34:13-37
90            android:windowSoftInputMode="adjustPan" />
90-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:36:13-52
91        <activity
91-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:38:9-43:15
92            android:name="com.anginatech.textrepeater.Stylish_Font_Activity"
92-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:39:13-50
93            android:configChanges="uiMode|screenSize|orientation"
93-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:40:13-66
94            android:exported="false"
94-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:41:13-37
95            android:windowSoftInputMode="adjustPan" />
95-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:42:13-52
96        <activity
96-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:44:9-49:15
97            android:name="com.anginatech.textrepeater.Text_to_Imoji_Activity"
97-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:45:13-51
98            android:configChanges="uiMode|screenSize|orientation"
98-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:46:13-66
99            android:exported="false"
99-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:47:13-37
100            android:windowSoftInputMode="adjustPan" />
100-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:48:13-52
101        <activity
101-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:50:9-55:15
102            android:name="com.anginatech.textrepeater.Blank_Text_Activity"
102-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:51:13-48
103            android:configChanges="uiMode|screenSize|orientation"
103-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:52:13-66
104            android:exported="false"
104-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:53:13-37
105            android:windowSoftInputMode="adjustPan" />
105-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:54:13-52
106        <activity
106-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:56:9-61:15
107            android:name="com.anginatech.textrepeater.Emoji_Art"
107-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:57:13-38
108            android:configChanges="uiMode|screenSize|orientation"
108-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:58:13-66
109            android:exported="false"
109-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:59:13-37
110            android:windowSoftInputMode="adjustPan" />
110-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:60:13-52
111        <activity
111-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:62:9-67:15
112            android:name="com.anginatech.textrepeater.Text_Repeat"
112-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:63:13-40
113            android:configChanges="uiMode|screenSize|orientation"
113-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:64:13-66
114            android:exported="false"
114-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:65:13-37
115            android:windowSoftInputMode="adjustPan" />
115-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:66:13-52
116        <activity
116-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:68:9-73:15
117            android:name="com.anginatech.textrepeater.Random_Text_Activity"
117-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:69:13-49
118            android:configChanges="uiMode|screenSize|orientation"
118-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:70:13-66
119            android:exported="false"
119-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:71:13-37
120            android:windowSoftInputMode="adjustPan" />
120-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:72:13-52
121        <activity
121-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:74:9-79:15
122            android:name="com.anginatech.textrepeater.Message_Activity"
122-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:75:13-45
123            android:configChanges="uiMode|screenSize|orientation"
123-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:76:13-66
124            android:exported="false"
124-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:77:13-37
125            android:windowSoftInputMode="adjustPan" />
125-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:78:13-52
126        <activity
126-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:80:9-85:15
127            android:name="com.anginatech.textrepeater.Settings_Activity"
127-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:81:13-46
128            android:configChanges="uiMode|screenSize|orientation"
128-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:82:13-66
129            android:exported="false"
129-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:83:13-37
130            android:windowSoftInputMode="adjustPan" />
130-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:84:13-52
131        <activity
131-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:86:9-91:15
132            android:name="com.anginatech.textrepeater.Navigation_Activity"
132-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:87:13-48
133            android:configChanges="uiMode|screenSize|orientation"
133-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:88:13-66
134            android:exported="false"
134-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:89:13-37
135            android:windowSoftInputMode="adjustPan" />
135-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:90:13-52
136        <activity
136-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:92:9-103:20
137            android:name="com.anginatech.textrepeater.Splash_Screen"
137-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:93:13-42
138            android:configChanges="uiMode|screenSize|orientation"
138-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:94:13-66
139            android:exported="true"
139-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:95:13-36
140            android:windowSoftInputMode="adjustPan" >
140-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:96:13-52
141            <intent-filter>
141-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:98:13-102:29
142                <action android:name="android.intent.action.MAIN" />
142-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:17-69
142-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:25-66
143
144                <category android:name="android.intent.category.LAUNCHER" />
144-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:17-77
144-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:27-74
145            </intent-filter>
146        </activity>
147        <activity
147-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:104:9-109:15
148            android:name="com.anginatech.textrepeater.MainActivity"
148-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:105:13-41
149            android:configChanges="uiMode|screenSize|orientation"
149-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:106:13-66
150            android:exported="true"
150-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:107:13-36
151            android:windowSoftInputMode="adjustPan" />
151-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:108:13-52
152
153        <meta-data
153-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:112:9-114:57
154            android:name="preloaded_fonts"
154-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:113:13-43
155            android:resource="@array/preloaded_fonts" />
155-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:114:13-54
156        <meta-data
156-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:115:9-117:51
157            android:name="com.google.android.gms.ads.APPLICATION_ID"
157-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:116:13-69
158            android:value="@string/ADMOB_APP_ID" />
158-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:117:13-49
159
160        <property
160-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:119:9-123:15
161            android:name="android.adservices.AD_SERVICES_CONFIG"
161-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:120:13-65
162            android:resource="@xml/gma_ad_services_config" />
162-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:121:13-59
163
164        <!-- Firebase Cloud Messaging Service -->
165        <service
165-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:126:9-132:19
166            android:name="com.anginatech.textrepeater.MyFirebaseMessagingService"
166-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:127:13-55
167            android:exported="false" >
167-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:128:13-37
168            <intent-filter>
168-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
169                <action android:name="com.google.firebase.MESSAGING_EVENT" />
169-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
169-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
170            </intent-filter>
171        </service>
172
173        <!-- Firebase Cloud Messaging default notification icon -->
174        <meta-data
174-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:135:9-137:67
175            android:name="com.google.firebase.messaging.default_notification_icon"
175-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:136:13-83
176            android:resource="@drawable/ic_launcher_foreground" />
176-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:137:13-64
177
178        <!-- Firebase Cloud Messaging default notification color -->
179        <meta-data
179-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:140:9-142:46
180            android:name="com.google.firebase.messaging.default_notification_color"
180-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:141:13-84
181            android:resource="@color/love" />
181-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:142:13-43
182
183        <!-- Firebase Cloud Messaging default notification channel -->
184        <meta-data
184-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:145:9-147:59
185            android:name="com.google.firebase.messaging.default_notification_channel_id"
185-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:146:13-89
186            android:value="text_repeater_notifications" />
186-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:147:13-56
187
188        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
189        <activity
189-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
190            android:name="com.google.android.gms.ads.AdActivity"
190-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
191            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
191-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
192            android:exported="false"
192-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
193            android:theme="@android:style/Theme.Translucent" />
193-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
194
195        <provider
195-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
196            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
196-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
197            android:authorities="com.anginatech.textrepeater.mobileadsinitprovider"
197-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
198            android:exported="false"
198-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
199            android:initOrder="100" />
199-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
200
201        <service
201-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
202            android:name="com.google.android.gms.ads.AdService"
202-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
203            android:enabled="true"
203-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
204            android:exported="false" />
204-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
205
206        <activity
206-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
207            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
207-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
208            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
208-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
209            android:exported="false" />
209-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
210        <activity
210-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
211            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
211-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
212            android:excludeFromRecents="true"
212-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
213            android:exported="false"
213-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
214            android:launchMode="singleTask"
214-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
215            android:taskAffinity=""
215-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
216            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
216-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
217
218        <meta-data
218-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
219            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
219-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
220            android:value="true" />
220-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
221        <meta-data
221-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
222            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
222-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
223            android:value="true" />
223-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\2850d14ca099d5deb7d7b18ccad2e38e\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
224
225        <receiver
225-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
226            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
226-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
227            android:exported="true"
227-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
228            android:permission="com.google.android.c2dm.permission.SEND" >
228-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
229            <intent-filter>
229-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
230                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
230-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
230-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
231            </intent-filter>
232
233            <meta-data
233-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
234                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
234-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
235                android:value="true" />
235-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
236        </receiver>
237        <!--
238             FirebaseMessagingService performs security checks at runtime,
239             but set to not exported to explicitly avoid allowing another app to call it.
240        -->
241        <service
241-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
242            android:name="com.google.firebase.messaging.FirebaseMessagingService"
242-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
243            android:directBootAware="true"
243-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
244            android:exported="false" >
244-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
245            <intent-filter android:priority="-500" >
245-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
246                <action android:name="com.google.firebase.MESSAGING_EVENT" />
246-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
246-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
247            </intent-filter>
248        </service>
249        <service
249-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
250            android:name="com.google.firebase.components.ComponentDiscoveryService"
250-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
251            android:directBootAware="true"
251-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
252            android:exported="false" >
252-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
253            <meta-data
253-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
254                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
254-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
256            <meta-data
256-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
257                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
257-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2ef093f8073ea3b55c3ee8803923ed7\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
259            <meta-data
259-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
260                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
260-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\718f09568fcef5de54815bfe11101a05\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
262            <meta-data
262-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
263                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
263-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
265            <meta-data
265-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
266                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
266-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
267                android:value="com.google.firebase.components.ComponentRegistrar" />
267-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\b3086165e1b0463f83539f95f8766dc1\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
268            <meta-data
268-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
269                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
269-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
270                android:value="com.google.firebase.components.ComponentRegistrar" />
270-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\374496f1e7ca9614360bf3b7bf8a9e73\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
271            <meta-data
271-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
272                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
272-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
273                android:value="com.google.firebase.components.ComponentRegistrar" />
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
274            <meta-data
274-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
275                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
275-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
276                android:value="com.google.firebase.components.ComponentRegistrar" />
276-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c2af893b31223fd359b9f55e7db95257\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
277        </service>
278
279        <provider
279-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
280            android:name="com.google.firebase.provider.FirebaseInitProvider"
280-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
281            android:authorities="com.anginatech.textrepeater.firebaseinitprovider"
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
282            android:directBootAware="true"
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
283            android:exported="false"
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
284            android:initOrder="100" />
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\69d68da74ff6a071dbdd5bfdbbe2871e\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
285
286        <receiver
286-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
287            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
287-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
288            android:enabled="true"
288-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
289            android:exported="false" >
289-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
290        </receiver>
291
292        <service
292-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
293            android:name="com.google.android.gms.measurement.AppMeasurementService"
293-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
294            android:enabled="true"
294-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
295            android:exported="false" />
295-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
296        <service
296-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
297            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
297-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
298            android:enabled="true"
298-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
299            android:exported="false"
299-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
300            android:permission="android.permission.BIND_JOB_SERVICE" />
300-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\5e2b5d39a474b33f96e8c45b339c8630\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
301
302        <provider
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
303            android:name="androidx.startup.InitializationProvider"
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
304            android:authorities="com.anginatech.textrepeater.androidx-startup"
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
305            android:exported="false" >
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
306            <meta-data
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
307                android:name="androidx.work.WorkManagerInitializer"
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
308                android:value="androidx.startup" />
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
309            <meta-data
309-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
310                android:name="androidx.emoji2.text.EmojiCompatInitializer"
310-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
311                android:value="androidx.startup" />
311-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\c003771cb65d251b08def97b4cbcbc8a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
312            <meta-data
312-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
313                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
313-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
314                android:value="androidx.startup" />
314-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\cc48b618a04026ac674c181092a8a43f\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
315            <meta-data
315-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
316                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
316-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
317                android:value="androidx.startup" />
317-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
318        </provider>
319
320        <service
320-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
321            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
321-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
322            android:directBootAware="false"
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
323            android:enabled="@bool/enable_system_alarm_service_default"
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
324            android:exported="false" />
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
325        <service
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
326            android:name="androidx.work.impl.background.systemjob.SystemJobService"
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
327            android:directBootAware="false"
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
328            android:enabled="@bool/enable_system_job_service_default"
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
329            android:exported="true"
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
330            android:permission="android.permission.BIND_JOB_SERVICE" />
330-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
331        <service
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
332            android:name="androidx.work.impl.foreground.SystemForegroundService"
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
334            android:enabled="@bool/enable_system_foreground_service_default"
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
335            android:exported="false" />
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
336
337        <receiver
337-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
338            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
340            android:enabled="true"
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
341            android:exported="false" />
341-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
342        <receiver
342-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
343-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
345            android:enabled="false"
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
348                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
349                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
350            </intent-filter>
351        </receiver>
352        <receiver
352-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
353            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
353-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
354            android:directBootAware="false"
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
355            android:enabled="false"
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
356            android:exported="false" >
356-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
357            <intent-filter>
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
358                <action android:name="android.intent.action.BATTERY_OKAY" />
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
359                <action android:name="android.intent.action.BATTERY_LOW" />
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
360            </intent-filter>
361        </receiver>
362        <receiver
362-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
363            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
363-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
365            android:enabled="false"
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
366            android:exported="false" >
366-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
367            <intent-filter>
367-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
368                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
369                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
370            </intent-filter>
371        </receiver>
372        <receiver
372-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
373            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
373-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
375            android:enabled="false"
375-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
376            android:exported="false" >
376-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
377            <intent-filter>
377-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
378                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
379            </intent-filter>
380        </receiver>
381        <receiver
381-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
382            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
382-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
383            android:directBootAware="false"
383-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
384            android:enabled="false"
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
385            android:exported="false" >
385-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
386            <intent-filter>
386-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
387                <action android:name="android.intent.action.BOOT_COMPLETED" />
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
388                <action android:name="android.intent.action.TIME_SET" />
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
389                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
390            </intent-filter>
391        </receiver>
392        <receiver
392-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
393            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
394            android:directBootAware="false"
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
395            android:enabled="@bool/enable_system_alarm_service_default"
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
396            android:exported="false" >
396-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
397            <intent-filter>
397-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
398                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
399            </intent-filter>
400        </receiver>
401        <receiver
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
402            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
403            android:directBootAware="false"
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
404            android:enabled="true"
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
405            android:exported="true"
405-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
406            android:permission="android.permission.DUMP" >
406-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
407            <intent-filter>
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
408                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\0710dfe4523a527fe8dc0bd298d4cfaa\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
409            </intent-filter>
410        </receiver>
411
412        <activity
412-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
413            android:name="com.google.android.gms.common.api.GoogleApiActivity"
413-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
414            android:exported="false"
414-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
415            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
415-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\917d0d7e0c096b9e28e4e727a3aa98ed\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
416
417        <uses-library
417-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
418            android:name="android.ext.adservices"
418-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
419            android:required="false" />
419-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\ee5194d5fa31e0bc5a9b072d2817fbf1\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
420
421        <service
421-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
422            android:name="androidx.room.MultiInstanceInvalidationService"
422-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
423            android:directBootAware="true"
423-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
424            android:exported="false" />
424-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\39655640ca949b70c7fbc19b8da94215\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
425
426        <meta-data
426-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
427            android:name="com.google.android.gms.version"
427-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
428            android:value="@integer/google_play_services_version" />
428-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\6a607bbfcc83cfb280a4c2f3d8ba8b9e\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
429
430        <service
430-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
431            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
431-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
432            android:exported="false" >
432-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
433            <meta-data
433-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
434                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
434-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
435                android:value="cct" />
435-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\93d017022c3c01feff83c95e617aca91\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
436        </service>
437        <service
437-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
438            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
438-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
439            android:exported="false"
439-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
440            android:permission="android.permission.BIND_JOB_SERVICE" >
440-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
441        </service>
442
443        <receiver
443-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
444            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
444-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
445            android:exported="false" />
445-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\431cfad28871a7f01f9814b63ec35259\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
446        <receiver
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
447            android:name="androidx.profileinstaller.ProfileInstallReceiver"
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
448            android:directBootAware="false"
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
449            android:enabled="true"
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
450            android:exported="true"
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
451            android:permission="android.permission.DUMP" >
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
452            <intent-filter>
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
453                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
454            </intent-filter>
455            <intent-filter>
455-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
456                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
457            </intent-filter>
458            <intent-filter>
458-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
459                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
460            </intent-filter>
461            <intent-filter>
461-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
462                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\9a8b03ec28b5ef9cb83fff7f4756a81e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
463            </intent-filter>
464        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
465        <activity
465-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
466            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
466-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
467            android:exported="false"
467-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
468            android:stateNotNeeded="true"
468-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
469            android:theme="@style/Theme.PlayCore.Transparent" />
469-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14.1\transforms\648a9db6d4e53069a7a01a3db8b333bb\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
470    </application>
471
472</manifest>
