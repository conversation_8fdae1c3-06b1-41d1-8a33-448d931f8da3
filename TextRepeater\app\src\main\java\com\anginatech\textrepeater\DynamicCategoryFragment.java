package com.anginatech.textrepeater;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * Dynamic Category Fragment that displays messages for any category
 * Simplified version for basic functionality
 */
public class DynamicCategoryFragment extends Fragment {

    private static final String ARG_CATEGORY = "category";
    private static final String ARG_USE_LEGACY_DB = "use_legacy_db";

    /**
     * Create new instance of DynamicCategoryFragment
     */
    public static DynamicCategoryFragment newInstance(Object category, boolean useLegacyDb) {
        DynamicCategoryFragment fragment = new DynamicCategoryFragment();
        Bundle args = new Bundle();
        if (category instanceof java.io.Serializable) {
            args.putSerializable(ARG_CATEGORY, (java.io.Serializable) category);
        }
        args.putBoolean(ARG_USE_LEGACY_DB, useLegacyDb);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // Return a simple view for now
        View view = inflater.inflate(R.layout.fragment_dynamic_category, container, false);
        return view;
    }
}
