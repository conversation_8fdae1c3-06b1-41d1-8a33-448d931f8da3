<!-- item_sms.xml -->
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smsItemClick"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        >


        <androidx.cardview.widget.CardView
            android:id="@+id/cardView"
            android:layout_width="36dp"
            android:layout_height="48dp"
            android:layout_marginStart="10dp"
            android:backgroundTint="#31F43636"
            app:cardCornerRadius="50dp"
            app:cardElevation="0dp"
            android:padding="10dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            >


            <TextView
                android:id="@+id/positionText"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="1"
                android:textSize="19sp"
                android:textColor="@color/primary_text"
                android:gravity="center"
                android:fontFamily="@font/alice"
                />

        </androidx.cardview.widget.CardView>


        <TextView
            android:id="@+id/smsText"
            android:layout_width="285dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:paddingEnd="16dp"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/artifika"
            android:text="Hello"
            android:paddingBottom="6dp"
            android:paddingTop="6dp"
            android:gravity="start"
            android:lineSpacingExtra="2dp"
            android:layout_marginEnd="20dp"
            android:textColor="@color/primary_text"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/cardView"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.506" />


        <View
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/smsText"
            app:layout_constraintVertical_bias="1.0"
            android:background="@color/emoji_view_color"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
