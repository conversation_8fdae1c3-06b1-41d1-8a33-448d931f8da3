<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/mother_layout_color"
    android:gravity="center">

    <!-- Header -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_launcher_foreground"
        android:background="@drawable/constant_second_back"
        android:padding="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Notification Permission"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/primary_text"
        android:fontFamily="@font/allerta"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Allow notifications to receive important updates and messages from Text Repeater."
        android:textSize="16sp"
        android:textColor="@color/secondary_text"
        android:fontFamily="@font/alice"
        android:gravity="center"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="32dp" />

    <!-- Status Text -->
    <TextView
        android:id="@+id/status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Status: Checking..."
        android:textSize="14sp"
        android:textColor="@color/secondary_text"
        android:fontFamily="@font/alice"
        android:gravity="center"
        android:padding="12dp"
        android:background="@drawable/constant_second_back"
        android:layout_marginBottom="24dp" />

    <!-- Request Permission Button -->
    <Button
        android:id="@+id/request_button"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Allow Notifications"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:background="@color/love"
        android:fontFamily="@font/allerta"
        android:layout_marginBottom="16dp"
        android:elevation="2dp" />

    <!-- Settings Button -->
    <Button
        android:id="@+id/settings_button"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="Open Settings"
        android:textSize="16sp"
        android:textColor="@color/love"
        android:background="@drawable/constant_second_back"
        android:fontFamily="@font/allerta"
        android:layout_marginBottom="32dp" />

    <!-- Info Text -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="You can change notification settings anytime in your device settings."
        android:textSize="12sp"
        android:textColor="@color/secondary_text"
        android:fontFamily="@font/alice"
        android:gravity="center"
        android:lineSpacingExtra="2dp" />

</LinearLayout>
