<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/drawer_color_item"
    android:orientation="vertical"
    >


    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"


        />

    <TextView
        android:id="@+id/textView5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select Emoji"
        android:textSize="19sp"
        android:fontFamily="@font/allerta"
        android:gravity="center"
        android:textColor="@color/secondary_text"
        />



    <GridView
        android:id="@+id/emoji_GridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:columnWidth="60dp"
        android:numColumns="4"
        android:layout_marginTop="16dp"
        android:horizontalSpacing="16dp"
        android:verticalSpacing="16dp"
        android:stretchMode="columnWidth"
        android:layout_marginStart="12dp"
        />









</LinearLayout>