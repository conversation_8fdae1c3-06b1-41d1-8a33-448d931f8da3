package com.anginatech.textrepeater;

import android.database.Cursor;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.List;


public class Sad_Fragment extends Fragment {

    // private DatabaseHelper databaseHelper; // Removed - now using Room database
    private RecyclerView recyclerView;
    private SmsAdapter adapter;
    private List<String> smsList = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
       View myView = inflater.inflate(R.layout.fragment_sad_, container, false);
       if (container!=null){
           container.removeAllViews();
       }


        // databaseHelper = new DatabaseHelper(getContext()); // Removed - now using Room database
        recyclerView = myView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false));
        recyclerView.setHasFixedSize(true);

        loadSms("sadTable");



       return myView;
    }


    private void loadSms(String catagory) {
        // Updated to use Room database instead of legacy SQLite
        MyApplication app = (MyApplication) requireActivity().getApplication();
        if (app.getRoomRepository() != null) {
            // Map legacy table names to modern category names
            String categoryName = mapLegacyTableToCategory(catagory);

            app.getRoomRepository().getMessagesByCategory(categoryName)
                .subscribeOn(io.reactivex.rxjava3.schedulers.Schedulers.io())
                .observeOn(io.reactivex.rxjava3.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe(
                    messages -> {
                        smsList.clear();
                        smsList.addAll(messages);
                        adapter = new SmsAdapter(requireContext(), smsList);
                        recyclerView.setAdapter(adapter);
                    },
                    error -> {
                        Toast.makeText(requireContext(), "Something went wrong! Please check your internet connection", Toast.LENGTH_SHORT).show();
                    }
                );
        } else {
            Toast.makeText(requireContext(), "Database not available", Toast.LENGTH_SHORT).show();
        }
    }

    private String mapLegacyTableToCategory(String legacyTable) {
        switch (legacyTable) {
            case "funnyTable":
                return "funny";
            case "romanticTable":
                return "romantic";
            case "sadTable":
                return "sad";
            default:
                return legacyTable;
        }
    }





}